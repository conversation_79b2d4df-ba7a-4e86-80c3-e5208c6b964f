"""
BlendPro Vision System Integration Test
Test script to verify vision system functionality

Author: inkbytefo
"""

import sys
import os

# Add the current directory to Python path for testing
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

def test_vision_dependencies():
    """Test if vision dependencies are available"""
    print("Testing Vision Dependencies...")
    
    dependencies = {
        "numpy": False,
        "pillow": False,
        "opencv": False
    }
    
    try:
        import numpy as np
        dependencies["numpy"] = True
        print("✓ NumPy available")
    except ImportError:
        print("✗ NumPy not available")
    
    try:
        from PIL import Image
        dependencies["pillow"] = True
        print("✓ Pillow available")
    except ImportError:
        print("✗ Pillow not available")
    
    try:
        import cv2
        dependencies["opencv"] = True
        print("✓ OpenCV available")
    except ImportError:
        print("✗ OpenCV not available")
    
    return dependencies

def test_vision_utilities_import():
    """Test if vision utilities can be imported"""
    print("\nTesting Vision Utilities Import...")
    
    try:
        # This will fail outside Blender, but we can test the import structure
        from vision_utilities import (
            check_vision_dependencies,
            get_vision_system_status,
            VISION_CONFIG
        )
        print("✓ Vision utilities import successful")
        return True
    except ImportError as e:
        print(f"✗ Vision utilities import failed: {e}")
        return False
    except Exception as e:
        print(f"⚠ Vision utilities import partial (expected outside Blender): {e}")
        return True  # This is expected outside Blender

def test_vision_config():
    """Test vision configuration"""
    print("\nTesting Vision Configuration...")
    
    try:
        from vision_utilities import VISION_CONFIG
        
        required_keys = [
            "default_screenshot_resolution",
            "max_objects_in_summary", 
            "screenshot_quality",
            "vision_model_default",
            "enable_multi_view",
            "enable_scene_caching"
        ]
        
        for key in required_keys:
            if key in VISION_CONFIG:
                print(f"✓ Config key '{key}': {VISION_CONFIG[key]}")
            else:
                print(f"✗ Missing config key: {key}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Vision config test failed: {e}")
        return False

def test_integration_points():
    """Test integration points with main addon"""
    print("\nTesting Integration Points...")
    
    # Test if __init__.py can import vision utilities
    try:
        # Simulate the import that happens in __init__.py
        print("Testing __init__.py integration...")
        
        # Check if the import structure is correct
        import vision_utilities
        print("✓ Vision utilities module structure OK")
        
        # Check if required functions exist
        required_functions = [
            'extract_comprehensive_scene_data',
            'capture_viewport_screenshot',
            'analyze_scene_with_vision',
            'test_vision_system'
        ]
        
        for func_name in required_functions:
            if hasattr(vision_utilities, func_name):
                print(f"✓ Function '{func_name}' available")
            else:
                print(f"✗ Missing function: {func_name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Integration test failed: {e}")
        return False

def test_utilities_integration():
    """Test utilities.py integration"""
    print("\nTesting Utilities Integration...")
    
    try:
        from utilities import generate_blender_code
        
        # Check if the function signature includes use_vision parameter
        import inspect
        sig = inspect.signature(generate_blender_code)
        
        if 'use_vision' in sig.parameters:
            print("✓ generate_blender_code has use_vision parameter")
            return True
        else:
            print("✗ generate_blender_code missing use_vision parameter")
            return False
            
    except Exception as e:
        print(f"✗ Utilities integration test failed: {e}")
        return False

def run_all_tests():
    """Run all vision system tests"""
    print("=" * 60)
    print("BlendPro Vision System Integration Test")
    print("=" * 60)
    
    test_results = {
        "dependencies": test_vision_dependencies(),
        "utilities_import": test_vision_utilities_import(),
        "config": test_vision_config(),
        "integration": test_integration_points(),
        "utilities_integration": test_utilities_integration()
    }
    
    print("\n" + "=" * 60)
    print("TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "PASS" if result else "FAIL"
        print(f"{test_name.upper()}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Vision system integration is ready.")
    elif passed >= total * 0.7:
        print("⚠ Most tests passed. Some issues may need attention.")
    else:
        print("❌ Multiple test failures. Please check the implementation.")
    
    return test_results

def print_installation_guide():
    """Print installation guide for missing dependencies"""
    print("\n" + "=" * 60)
    print("INSTALLATION GUIDE")
    print("=" * 60)
    
    print("To install missing dependencies:")
    print("1. Open command prompt/terminal as administrator")
    print("2. Navigate to Blender's Python directory:")
    print("   Windows: C:\\Program Files\\Blender Foundation\\Blender [version]\\[version]\\python\\bin")
    print("   macOS: /Applications/Blender.app/Contents/Resources/[version]/python/bin")
    print("   Linux: /usr/share/blender/[version]/python/bin")
    print("3. Run the following commands:")
    print("   python -m pip install opencv-python>=4.8.0")
    print("   python -m pip install pillow>=10.0.0")
    print("   python -m pip install numpy>=1.24.0")
    print("\nAlternatively, use Blender's built-in pip:")
    print("   import subprocess")
    print("   import sys")
    print("   subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'opencv-python', 'pillow', 'numpy'])")

if __name__ == "__main__":
    # Run tests
    results = run_all_tests()
    
    # Print installation guide if dependencies are missing
    deps = results.get("dependencies", {})
    if isinstance(deps, dict) and not all(deps.values()):
        print_installation_guide()
    
    print("\n" + "=" * 60)
    print("Test completed. Check results above.")
    print("=" * 60)
