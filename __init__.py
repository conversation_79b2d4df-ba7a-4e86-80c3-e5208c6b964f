import sys
import os
import bpy
import bpy.props
import re

# Add the 'libs' folder to the Python path
libs_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), "lib")
if libs_path not in sys.path:
    sys.path.append(libs_path)

# OpenAI client will be created when needed

from .utilities import *

# Import vision utilities with error handling
try:
    from .vision_utilities import *
    VISION_SYSTEM_AVAILABLE = True
    print("BlendPro Vision System loaded successfully")
except ImportError as e:
    print(f"Vision System not available: {e}")
    VISION_SYSTEM_AVAILABLE = False
except Exception as e:
    print(f"Error loading Vision System: {e}")
    VISION_SYSTEM_AVAILABLE = False
bl_info = {
    "name": "BlendPro - Advanced AI Blender Assistant",
    "blender": (2, 82, 0),
    "category": "Object",
    "author": "inkbytefo",
    "version": (3, 0, 0),
    "location": "3D View > UI > GPT-4 Blender Assistant",
    "description": "Advanced AI-powered Blender assistant with code preview, auto-save, export/import, and undo features.",
    "warning": "",
    "wiki_url": "https://github.com/inkbytefo/BlendPro",
    "tracker_url": "https://github.com/inkbytefo/BlendPro/issues",
}

system_prompt = """You are an assistant made for the purposes of helping the user with Blender, the 3D software. 
- Respond with your answers in markdown (```). 
- Preferably import entire modules instead of bits. 
- Do not perform destructive operations on the meshes. 
- Do not use cap_ends. Do not do more than what is asked (setting up render settings, adding cameras, etc)
- Do not respond with anything that is not Python code.

Example:

user: create 10 cubes in random locations from -10 to 10
assistant:
```
import bpy
import random
bpy.ops.mesh.primitive_cube_add()

#how many cubes you want to add
count = 10

for c in range(0,count):
    x = random.randint(-10,10)
    y = random.randint(-10,10)
    z = random.randint(-10,10)
    bpy.ops.mesh.primitive_cube_add(location=(x,y,z))
```"""



class BLENDPRO_OT_DeleteMessage(bpy.types.Operator):
    bl_idname = "blendpro.delete_message"
    bl_label = "Delete Message"
    bl_options = {'REGISTER', 'UNDO'}

    message_index: bpy.props.IntProperty()

    def execute(self, context):
        context.scene.blendpro_chat_history.remove(self.message_index)
        return {'FINISHED'}

class BLENDPRO_OT_ShowCode(bpy.types.Operator):
    bl_idname = "blendpro.show_code"
    bl_label = "Show Code"
    bl_options = {'REGISTER', 'UNDO'}

    code: bpy.props.StringProperty(
        name="Code",
        description="The generated code",
        default="",
    )

    def execute(self, context):
        text_name = "BLENDPRO_Generated_Code.py"
        text = bpy.data.texts.get(text_name)
        if text is None:
            text = bpy.data.texts.new(text_name)

        text.clear()
        text.write(self.code)

        text_editor_area = None
        for area in context.screen.areas:
            if area.type == 'TEXT_EDITOR':
                text_editor_area = area
                break

        if text_editor_area is None:
            text_editor_area = split_area_to_text_editor(context)

        text_editor_area.spaces.active.text = text

        return {'FINISHED'}

class BLENDPRO_PT_Panel(bpy.types.Panel):
    bl_label = "BlendPro Blender Assistant"
    bl_idname = "BLENDPRO_PT_Panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'BlendPro Assistant'

    def draw(self, context):
        layout = self.layout
        column = layout.column(align=True)

        column.label(text="Chat history:")
        box = column.box()
        for index, message in enumerate(context.scene.blendpro_chat_history):
            if message.type == 'assistant':
                row = box.row()
                row.label(text="Assistant: ")
                show_code_op = row.operator("blendpro.show_code", text="Show Code")
                show_code_op.code = message.content
                delete_message_op = row.operator("blendpro.delete_message", text="", icon="TRASH", emboss=False)
                delete_message_op.message_index = index
            else:
                row = box.row()
                row.label(text=f"User: {message.content}")
                delete_message_op = row.operator("blendpro.delete_message", text="", icon="TRASH", emboss=False)
                delete_message_op.message_index = index

        column.separator()



        # Get addon preferences to check if custom model is being used
        preferences = context.preferences
        addon_prefs = preferences.addons[__name__].preferences

        if addon_prefs.use_custom_model and addon_prefs.custom_model:
            column.label(text=f"Model: {addon_prefs.custom_model} (Custom)")
        else:
            column.label(text="GPT Model:")
            column.prop(context.scene, "blendpro_model", text="")

        column.label(text="Enter your message:")
        column.prop(context.scene, "blendpro_chat_input", text="")
        button_label = "Please wait...(this might take some time)" if context.scene.blendpro_button_pressed else "Execute"
        row = column.row(align=True)
        row.operator("blendpro.send_message", text=button_label)
        row.operator("blendpro.clear_chat", text="Clear Chat")

        # Export/Import buttons
        row = column.row(align=True)
        row.operator("blendpro.export_chat", text="Export Chat", icon="EXPORT")
        row.operator("blendpro.import_chat", text="Import Chat", icon="IMPORT")

        # Undo/Backup buttons
        row = column.row(align=True)
        row.operator("blendpro.undo_operation", text="Undo Last", icon="LOOP_BACK")
        row.operator("blendpro.show_backups", text="Show Backups", icon="FILE_BACKUP")
        row.operator("blendpro.cleanup_backups", text="Cleanup", icon="TRASH")

        column.separator()

        # Vision System Section
        if VISION_SYSTEM_AVAILABLE:
            box = column.box()
            box.label(text="AI Vision System", icon="CAMERA_DATA")

            # Vision analysis button
            vision_button_label = "Analyzing Scene..." if context.scene.blendpro_button_pressed else "Analyze Scene with Vision"
            box.operator("blendpro.analyze_scene_vision", text=vision_button_label, icon="VIEWZOOM")

            # Vision utilities row
            vision_row = box.row(align=True)
            vision_row.operator("blendpro.capture_screenshot", text="Screenshot", icon="CAMERA_DATA")
            vision_row.operator("blendpro.test_vision_system", text="Test Vision", icon="EXPERIMENTAL")

            # Vision status indicator
            try:
                from .vision_utilities import get_vision_system_status
                vision_status = get_vision_system_status()
                if vision_status.get("available", False):
                    box.label(text="✓ Vision System Ready", icon="CHECKMARK")
                else:
                    missing_features = [k for k, v in vision_status.get("features", {}).items() if not v]
                    if missing_features:
                        box.label(text=f"⚠ Limited: {', '.join(missing_features[:2])}", icon="ERROR")
                    else:
                        box.label(text="⚠ Vision System Issues", icon="ERROR")
            except:
                box.label(text="⚠ Vision Status Unknown", icon="QUESTION")
        else:
            box = column.box()
            box.label(text="AI Vision System", icon="CAMERA_DATA")
            box.label(text="Vision system not available", icon="ERROR")
            box.label(text="Install: pip install opencv-python pillow numpy")

        column.separator()

class BLENDPRO_OT_ClearChat(bpy.types.Operator):
    bl_idname = "blendpro.clear_chat"
    bl_label = "Clear Chat"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        context.scene.blendpro_chat_history.clear()
        # Auto-save empty chat history
        from .utilities import save_chat_history
        save_chat_history(context.scene.blendpro_chat_history)
        return {'FINISHED'}

class BLENDPRO_OT_ExportChat(bpy.types.Operator):
    bl_idname = "blendpro.export_chat"
    bl_label = "Export Chat History"
    bl_options = {'REGISTER', 'UNDO'}

    filepath: bpy.props.StringProperty(
        name="File Path",
        description="Choose a file path to export chat history",
        default="blendpro_chat_export.json",
        subtype='FILE_PATH'
    )

    def execute(self, context):
        try:
            import json
            history_data = []

            for message in context.scene.blendpro_chat_history:
                history_data.append({
                    "type": message.type,
                    "content": message.content
                })

            with open(self.filepath, 'w', encoding='utf-8') as f:
                json.dump(history_data, f, indent=2, ensure_ascii=False)

            self.report({'INFO'}, f"Chat history exported to: {self.filepath}")
            return {'FINISHED'}
        except Exception as e:
            self.report({'ERROR'}, f"Error exporting chat history: {e}")
            return {'CANCELLED'}

    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

class BLENDPRO_OT_ImportChat(bpy.types.Operator):
    bl_idname = "blendpro.import_chat"
    bl_label = "Import Chat History"
    bl_options = {'REGISTER', 'UNDO'}

    filepath: bpy.props.StringProperty(
        name="File Path",
        description="Choose a file path to import chat history",
        default="",
        subtype='FILE_PATH'
    )

    def execute(self, context):
        try:
            import json

            with open(self.filepath, 'r', encoding='utf-8') as f:
                history_data = json.load(f)

            # Clear existing history
            context.scene.blendpro_chat_history.clear()

            # Load imported history
            for item in history_data:
                message = context.scene.blendpro_chat_history.add()
                message.type = item.get("type", "user")
                message.content = item.get("content", "")

            # Auto-save imported history
            from .utilities import save_chat_history
            save_chat_history(context.scene.blendpro_chat_history)

            self.report({'INFO'}, f"Chat history imported from: {self.filepath}")
            return {'FINISHED'}
        except Exception as e:
            self.report({'ERROR'}, f"Error importing chat history: {e}")
            return {'CANCELLED'}

    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

class BLENDPRO_OT_CodePreview(bpy.types.Operator):
    bl_idname = "blendpro.code_preview"
    bl_label = "Code Preview"
    bl_options = {'REGISTER', 'UNDO'}

    code: bpy.props.StringProperty(
        name="Generated Code",
        description="The generated Python code to preview",
        default=""
    )

    def execute(self, context):
        # Save scene state before executing code
        from .utilities import save_scene_state, cleanup_old_backups
        backup_path = save_scene_state()

        # Execute the code
        try:
            global_namespace = globals().copy()
            exec(self.code, global_namespace)
            self.report({'INFO'}, "Code executed successfully! (Backup saved for undo)")

            # Clean up old backups periodically
            cleanup_old_backups(10)

            return {'FINISHED'}
        except Exception as e:
            self.report({'ERROR'}, f"Error executing code: {e}")
            return {'CANCELLED'}

    def invoke(self, context, event):
        return context.window_manager.invoke_props_dialog(self, width=600)

    def draw(self, context):
        layout = self.layout
        layout.label(text="Generated Code Preview:", icon="FILE_SCRIPT")

        # Create a scrollable text area
        box = layout.box()
        lines = self.code.split('\n')

        # Show first 20 lines with line numbers
        for i, line in enumerate(lines[:20]):
            row = box.row()
            row.alignment = 'LEFT'
            row.label(text=f"{i+1:3d}: {line}")

        if len(lines) > 20:
            box.label(text=f"... and {len(lines) - 20} more lines")

        layout.separator()
        layout.label(text="Do you want to execute this code?", icon="QUESTION")

class BLENDPRO_OT_UndoOperation(bpy.types.Operator):
    bl_idname = "blendpro.undo_operation"
    bl_label = "Undo Last Operation"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        try:
            from .utilities import get_recent_backups
            backups = get_recent_backups(2)  # Get last 2 backups

            if len(backups) >= 2:
                # Load the previous backup (second most recent)
                backup_path = backups[1]
                bpy.ops.wm.open_mainfile(filepath=backup_path)
                self.report({'INFO'}, f"Undone to previous state: {os.path.basename(backup_path)}")
                return {'FINISHED'}
            else:
                self.report({'WARNING'}, "No previous state available for undo")
                return {'CANCELLED'}
        except Exception as e:
            self.report({'ERROR'}, f"Error during undo: {e}")
            return {'CANCELLED'}

class BLENDPRO_OT_ShowBackups(bpy.types.Operator):
    bl_idname = "blendpro.show_backups"
    bl_label = "Show Recent Backups"
    bl_options = {'REGISTER'}

    def execute(self, context):
        from .utilities import get_recent_backups
        backups = get_recent_backups(10)

        if backups:
            self.report({'INFO'}, f"Found {len(backups)} recent backups")
            for i, backup in enumerate(backups):
                print(f"{i+1}. {os.path.basename(backup)} - {os.path.getmtime(backup)}")
        else:
            self.report({'INFO'}, "No backups found")

        return {'FINISHED'}

class BLENDPRO_OT_CleanupBackups(bpy.types.Operator):
    bl_idname = "blendpro.cleanup_backups"
    bl_label = "Cleanup Old Backups"
    bl_options = {'REGISTER'}

    def execute(self, context):
        try:
            from .utilities import cleanup_old_backups
            cleanup_old_backups(5)  # Keep only 5 most recent
            self.report({'INFO'}, "Old backups cleaned up")
            return {'FINISHED'}
        except Exception as e:
            self.report({'ERROR'}, f"Error cleaning backups: {e}")
            return {'CANCELLED'}

class BLENDPRO_OT_Execute(bpy.types.Operator):
    bl_idname = "blendpro.send_message"
    bl_label = "Send Message"
    bl_options = {'REGISTER', 'UNDO'}

    natural_language_input: bpy.props.StringProperty(
        name="Command",
        description="Enter the natural language command",
        default="",
    )

    # Background processing variables
    _timer = None
    _thread = None
    _result = None
    _error = None
    _processing = False

    def execute(self, context):
        # Check if already processing
        if self._processing:
            self.report({'WARNING'}, "Already processing a request. Please wait...")
            return {'CANCELLED'}

        # Get preferences
        preferences = context.preferences
        addon_prefs = preferences.addons[__name__].preferences

        # Get API key from addon preferences or environment
        api_key = addon_prefs.api_key
        if not api_key:
            api_key = os.getenv("OPENAI_API_KEY")

        if not api_key:
            self.report({'ERROR'}, "No API key detected. Please set the API key in the addon preferences.")
            return {'CANCELLED'}

        # Get base URL from preferences or environment
        base_url = addon_prefs.base_url
        if not base_url:
            base_url = os.getenv("OPENAI_BASE_URL")

        # Get model selection
        model = None
        if addon_prefs.use_custom_model and addon_prefs.custom_model:
            model = addon_prefs.custom_model

        # Store user input and add to chat history
        user_input = context.scene.blendpro_chat_input
        if not user_input.strip():
            self.report({'ERROR'}, "Please enter a message.")
            return {'CANCELLED'}

        message = context.scene.blendpro_chat_history.add()
        message.type = 'user'
        message.content = user_input

        # Auto-save chat history after adding user message
        from .utilities import save_chat_history
        save_chat_history(context.scene.blendpro_chat_history)

        # Clear the chat input field
        context.scene.blendpro_chat_input = ""

        # Set processing state
        context.scene.blendpro_button_pressed = True
        self._processing = True
        self._result = None
        self._error = None

        # Start background processing
        import threading
        self._thread = threading.Thread(
            target=self._background_process,
            args=(user_input, context.scene.blendpro_chat_history, context, api_key, base_url, model)
        )
        self._thread.daemon = True
        self._thread.start()

        # Start timer to check for completion
        wm = context.window_manager
        self._timer = wm.event_timer_add(0.1, window=context.window)
        wm.modal_handler_add(self)

        return {'RUNNING_MODAL'}

    def _background_process(self, user_input, chat_history, context, api_key, base_url, model):
        """Background thread function for API call"""
        try:
            # Get addon preferences for AI configuration
            preferences = context.preferences
            addon_prefs = preferences.addons[__name__].preferences

            # Check if vision context should be used (detect vision-related keywords)
            use_vision_context = False
            if VISION_SYSTEM_AVAILABLE and addon_prefs.enable_vision_context:
                # Get keywords from preferences
                keywords = [k.strip().lower() for k in addon_prefs.auto_vision_keywords.split(',') if k.strip()]
                use_vision_context = any(keyword in user_input.lower() for keyword in keywords)

            # Use standard generation with optional vision context
            from .utilities import generate_blender_code
            result = generate_blender_code(
                user_input,
                chat_history,
                context,
                system_prompt,
                api_key,
                base_url,
                model,
                timeout=60,  # 60 second timeout
                temperature=addon_prefs.temperature,
                max_tokens=addon_prefs.max_tokens,
                top_p=addon_prefs.top_p,
                use_vision=use_vision_context
            )
            self._result = result
        except Exception as e:
            self._error = f"Background processing error: {str(e)}"

    def modal(self, context, event):
        """Modal handler to check background process completion"""
        if event.type == 'TIMER':
            # Check if thread is still alive
            if self._thread and self._thread.is_alive():
                return {'PASS_THROUGH'}

            # Thread completed, process results
            wm = context.window_manager
            wm.event_timer_remove(self._timer)
            self._timer = None

            context.scene.blendpro_button_pressed = False
            self._processing = False

            # Handle errors
            if self._error:
                self.report({'ERROR'}, self._error)
                return {'CANCELLED'}

            if not self._result:
                self.report({'ERROR'}, "No response received from API")
                return {'CANCELLED'}

            # Check for API errors
            if self._result.get('error'):
                error_msg = self._result['error']
                if 'timeout' in error_msg.lower():
                    self.report({'ERROR'}, "Request timed out. Please try again.")
                elif 'rate limit' in error_msg.lower():
                    self.report({'ERROR'}, "API rate limit exceeded. Please wait and try again.")
                elif 'connection' in error_msg.lower():
                    self.report({'ERROR'}, "Connection error. Please check your internet connection.")
                else:
                    self.report({'ERROR'}, f"API Error: {error_msg}")
                return {'CANCELLED'}

            # Process successful result
            blender_code = self._result.get('code')
            if blender_code and blender_code.strip():
                # Add assistant response to chat history
                message = context.scene.blendpro_chat_history.add()
                message.type = 'assistant'
                message.content = blender_code

                # Auto-save chat history after adding assistant response
                from .utilities import save_chat_history
                save_chat_history(context.scene.blendpro_chat_history)

                # Show code preview instead of direct execution
                bpy.ops.blendpro.code_preview('INVOKE_DEFAULT', code=blender_code)
            else:
                # More detailed error reporting
                error_details = self._result.get('error', 'Unknown error')
                self.report({'ERROR'}, f"No code generated from API response. Details: {error_details}")
                print(f"Full API result: {self._result}")  # Debug output
                return {'CANCELLED'}

            return {'FINISHED'}

        return {'PASS_THROUGH'}


class BLENDPRO_OT_AnalyzeSceneVision(bpy.types.Operator):
    bl_idname = "blendpro.analyze_scene_vision"
    bl_label = "Analyze Scene with Vision"
    bl_description = "Analyze the current scene using AI vision capabilities"
    bl_options = {'REGISTER', 'UNDO'}

    user_prompt: bpy.props.StringProperty(
        name="Analysis Request",
        description="What would you like the AI to analyze about the scene?",
        default="Analyze this 3D scene and provide insights about the composition, lighting, and modeling"
    )

    # Background processing variables
    _timer = None
    _thread = None
    _result = None
    _error = None
    _processing = False

    def execute(self, context):
        if not VISION_SYSTEM_AVAILABLE:
            self.report({'ERROR'}, "Vision system not available. Please check dependencies.")
            return {'CANCELLED'}

        # Check if already processing
        if self._processing:
            self.report({'WARNING'}, "Vision analysis already in progress. Please wait...")
            return {'CANCELLED'}

        # Get preferences
        preferences = context.preferences
        addon_prefs = preferences.addons[__name__].preferences

        # Get API key
        api_key = addon_prefs.api_key
        if not api_key:
            api_key = os.getenv("OPENAI_API_KEY")

        if not api_key:
            self.report({'ERROR'}, "No API key detected. Please set the API key in addon preferences.")
            return {'CANCELLED'}

        # Get base URL
        base_url = addon_prefs.base_url
        if not base_url:
            base_url = os.getenv("OPENAI_BASE_URL")

        # Set processing state
        context.scene.blendpro_button_pressed = True
        self._processing = True
        self._result = None
        self._error = None

        # Start background processing
        import threading
        self._thread = threading.Thread(
            target=self._background_vision_analysis,
            args=(context, self.user_prompt, api_key, base_url)
        )
        self._thread.daemon = True
        self._thread.start()

        # Start timer to check for completion
        wm = context.window_manager
        self._timer = wm.event_timer_add(0.1, window=context.window)
        wm.modal_handler_add(self)

        return {'RUNNING_MODAL'}

    def _background_vision_analysis(self, context, user_prompt, api_key, base_url):
        """Background thread function for vision analysis"""
        try:
            from .vision_utilities import analyze_scene_with_vision
            result = analyze_scene_with_vision(
                context=context,
                user_prompt=user_prompt,
                api_key=api_key,
                base_url=base_url,
                model="gpt-4-vision-preview"
            )
            self._result = result
        except Exception as e:
            self._error = f"Vision analysis error: {str(e)}"

    def modal(self, context, event):
        """Modal handler to check background process completion"""
        if event.type == 'TIMER':
            # Check if thread is still alive
            if self._thread and self._thread.is_alive():
                return {'PASS_THROUGH'}

            # Thread completed, process results
            wm = context.window_manager
            wm.event_timer_remove(self._timer)
            self._timer = None

            context.scene.blendpro_button_pressed = False
            self._processing = False

            # Handle errors
            if self._error:
                self.report({'ERROR'}, self._error)
                return {'CANCELLED'}

            if not self._result:
                self.report({'ERROR'}, "No response received from vision API")
                return {'CANCELLED'}

            # Check for API errors
            if self._result.get('error'):
                self.report({'ERROR'}, f"Vision API Error: {self._result['error']}")
                return {'CANCELLED'}

            # Process successful result
            analysis = self._result.get('analysis', '')
            code = self._result.get('code')

            # Add vision analysis to chat history
            if analysis:
                message = context.scene.blendpro_chat_history.add()
                message.type = 'assistant'
                message.content = f"Vision Analysis: {analysis}"

            # If code was generated, add it to chat and show preview
            if code and code.strip():
                code_message = context.scene.blendpro_chat_history.add()
                code_message.type = 'assistant'
                code_message.content = code

                # Auto-save chat history
                from .utilities import save_chat_history
                save_chat_history(context.scene.blendpro_chat_history)

                # Show code preview
                bpy.ops.blendpro.code_preview('INVOKE_DEFAULT', code=code)
            else:
                # Just save the analysis
                from .utilities import save_chat_history
                save_chat_history(context.scene.blendpro_chat_history)

                self.report({'INFO'}, f"Vision analysis complete: {analysis[:100]}...")

            return {'FINISHED'}

        return {'PASS_THROUGH'}

    def invoke(self, context, event):
        return context.window_manager.invoke_props_dialog(self, width=400)

    def draw(self, context):
        layout = self.layout
        layout.label(text="Vision Scene Analysis", icon="CAMERA_DATA")
        layout.prop(self, "user_prompt")

class BLENDPRO_OT_CaptureScreenshot(bpy.types.Operator):
    bl_idname = "blendpro.capture_screenshot"
    bl_label = "Capture Screenshot"
    bl_description = "Capture current viewport screenshot"
    bl_options = {'REGISTER'}

    def execute(self, context):
        if not VISION_SYSTEM_AVAILABLE:
            self.report({'ERROR'}, "Vision system not available. Please check dependencies.")
            return {'CANCELLED'}

        try:
            from .vision_utilities import capture_viewport_screenshot
            screenshot = capture_viewport_screenshot(context)

            if screenshot:
                self.report({'INFO'}, "Screenshot captured successfully")
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, "Failed to capture screenshot")
                return {'CANCELLED'}

        except Exception as e:
            self.report({'ERROR'}, f"Screenshot capture failed: {str(e)}")
            return {'CANCELLED'}

class BLENDPRO_OT_TestVisionSystem(bpy.types.Operator):
    bl_idname = "blendpro.test_vision_system"
    bl_label = "Test Vision System"
    bl_description = "Test vision system functionality"
    bl_options = {'REGISTER'}

    def execute(self, context):
        if not VISION_SYSTEM_AVAILABLE:
            self.report({'ERROR'}, "Vision system not available. Please check dependencies.")
            return {'CANCELLED'}

        try:
            from .vision_utilities import test_vision_system
            test_results = test_vision_system(context)

            # Report results
            status = test_results.get("status", {})
            if status.get("available", False):
                self.report({'INFO'}, "Vision system is fully functional")
            else:
                missing_deps = [k for k, v in status.get("dependencies", {}).items() if not v]
                self.report({'WARNING'}, f"Vision system partially available. Missing: {', '.join(missing_deps)}")

            # Print detailed results to console
            print("Vision System Test Results:")
            print(f"Dependencies: {test_results.get('dependencies', {})}")
            print(f"Tests: {test_results.get('tests', {})}")

            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Vision system test failed: {str(e)}")
            return {'CANCELLED'}

class BLENDPRO_OT_TestConnection(bpy.types.Operator):
    bl_idname = "blendpro.test_connection"
    bl_label = "Test API Connection"
    bl_options = {'REGISTER'}

    def execute(self, context):
        preferences = context.preferences
        addon_prefs = preferences.addons[__name__].preferences

        api_key = addon_prefs.api_key
        if not api_key:
            api_key = os.getenv("OPENAI_API_KEY")

        if not api_key:
            self.report({'ERROR'}, "No API key found")
            return {'CANCELLED'}

        base_url = addon_prefs.base_url
        if not base_url:
            base_url = os.getenv("OPENAI_BASE_URL")

        model = addon_prefs.custom_model if addon_prefs.use_custom_model else context.scene.blendpro_model

        from .utilities import test_openrouter_connection
        result = test_openrouter_connection(api_key, base_url, model)

        if result["success"]:
            self.report({'INFO'}, f"Connection successful! Response: {result['content'][:50]}...")
        else:
            self.report({'ERROR'}, f"Connection failed: {result['error']}")

        return {'FINISHED'}




def menu_func(self, context):
    self.layout.operator(BLENDPRO_OT_Execute.bl_idname)

class BLENDPROAddonPreferences(bpy.types.AddonPreferences):
    bl_idname = __name__

    api_key: bpy.props.StringProperty(
        name="API Key",
        description="Enter your OpenAI API Key",
        default="",
        subtype="PASSWORD",
    )

    base_url: bpy.props.StringProperty(
        name="Base URL",
        description="OpenAI API Base URL (leave empty for default: https://api.openai.com/v1)",
        default="",
    )

    custom_model: bpy.props.StringProperty(
        name="Custom Model",
        description="Custom model ID (e.g., gpt-4-turbo, claude-3-opus, local-model)",
        default="",
    )

    use_custom_model: bpy.props.BoolProperty(
        name="Use Custom Model",
        description="Use custom model instead of predefined ones",
        default=False,
    )

    # AI Configuration Parameters
    temperature: bpy.props.FloatProperty(
        name="Temperature",
        description="Controls randomness in the output (0.0 = deterministic, 2.0 = very random)",
        default=0.7,
        min=0.0,
        max=2.0,
        step=0.1,
        precision=1,
    )

    max_tokens: bpy.props.IntProperty(
        name="Max Tokens",
        description="Maximum number of tokens to generate in the response",
        default=1500,
        min=1,
        max=4000,
    )

    top_p: bpy.props.FloatProperty(
        name="Top P",
        description="Controls diversity via nucleus sampling (0.1 = only top 10% of tokens)",
        default=1.0,
        min=0.0,
        max=1.0,
        step=0.1,
        precision=2,
    )

    # Vision System Configuration
    enable_vision_context: bpy.props.BoolProperty(
        name="Enable Vision Context",
        description="Automatically add scene context to AI requests when relevant keywords are detected",
        default=True,
    )

    vision_model: bpy.props.StringProperty(
        name="Vision Model",
        description="Model to use for vision analysis (e.g., gpt-4-vision-preview)",
        default="gpt-4-vision-preview",
    )

    auto_vision_keywords: bpy.props.StringProperty(
        name="Auto Vision Keywords",
        description="Comma-separated keywords that trigger automatic vision context",
        default="scene,current,visible,see,look,analyze,what,this,these,objects",
    )

    def draw(self, context):
        layout = self.layout
        layout.prop(self, "api_key")
        layout.separator()
        layout.prop(self, "base_url")
        layout.separator()
        layout.prop(self, "use_custom_model")
        if self.use_custom_model:
            layout.prop(self, "custom_model")
        layout.separator()

        # AI Configuration Section
        box = layout.box()
        box.label(text="AI Configuration:", icon="SETTINGS")
        box.prop(self, "temperature")
        box.prop(self, "max_tokens")
        box.prop(self, "top_p")

        layout.separator()

        # Vision System Configuration Section
        if VISION_SYSTEM_AVAILABLE:
            vision_box = layout.box()
            vision_box.label(text="Vision System Configuration:", icon="CAMERA_DATA")
            vision_box.prop(self, "enable_vision_context")
            vision_box.prop(self, "vision_model")
            vision_box.prop(self, "auto_vision_keywords")

            # Vision system status
            try:
                from .vision_utilities import get_vision_system_status
                vision_status = get_vision_system_status()
                if vision_status.get("available", False):
                    vision_box.label(text="✓ Vision System Fully Available", icon="CHECKMARK")
                else:
                    vision_box.label(text="⚠ Vision System Partially Available", icon="ERROR")
                    missing_deps = [k for k, v in vision_status.get("dependencies", {}).items() if not v]
                    if missing_deps:
                        vision_box.label(text=f"Missing: {', '.join(missing_deps)}")
            except Exception as e:
                vision_box.label(text=f"Vision Status Error: {str(e)}", icon="ERROR")
        else:
            vision_box = layout.box()
            vision_box.label(text="Vision System Configuration:", icon="CAMERA_DATA")
            vision_box.label(text="Vision system not available", icon="ERROR")
            vision_box.label(text="Install dependencies: pip install opencv-python pillow numpy")

        layout.separator()
        layout.operator("blendpro.test_connection", text="Test API Connection")

def register():
    bpy.utils.register_class(BLENDPROAddonPreferences)
    bpy.utils.register_class(BLENDPRO_OT_Execute)
    bpy.utils.register_class(BLENDPRO_PT_Panel)
    bpy.utils.register_class(BLENDPRO_OT_ClearChat)
    bpy.utils.register_class(BLENDPRO_OT_ExportChat)
    bpy.utils.register_class(BLENDPRO_OT_ImportChat)
    bpy.utils.register_class(BLENDPRO_OT_CodePreview)
    bpy.utils.register_class(BLENDPRO_OT_UndoOperation)
    bpy.utils.register_class(BLENDPRO_OT_ShowBackups)
    bpy.utils.register_class(BLENDPRO_OT_CleanupBackups)
    bpy.utils.register_class(BLENDPRO_OT_ShowCode)
    bpy.utils.register_class(BLENDPRO_OT_DeleteMessage)
    bpy.utils.register_class(BLENDPRO_OT_TestConnection)

    # Register vision system operators if available
    if VISION_SYSTEM_AVAILABLE:
        bpy.utils.register_class(BLENDPRO_OT_AnalyzeSceneVision)
        bpy.utils.register_class(BLENDPRO_OT_CaptureScreenshot)
        bpy.utils.register_class(BLENDPRO_OT_TestVisionSystem)

    bpy.types.VIEW3D_MT_mesh_add.append(menu_func)
    init_props()

    # Load saved chat history on addon startup
    def load_history_delayed():
        """Load chat history with a small delay to ensure context is ready"""
        try:
            if bpy.context and bpy.context.scene:
                from .utilities import load_chat_history
                load_chat_history(bpy.context)
        except Exception as e:
            print(f"Error loading chat history on startup: {e}")

    # Use a timer to delay loading slightly
    bpy.app.timers.register(load_history_delayed, first_interval=0.1)


def unregister():
    bpy.utils.unregister_class(BLENDPROAddonPreferences)
    bpy.utils.unregister_class(BLENDPRO_OT_Execute)
    bpy.utils.unregister_class(BLENDPRO_PT_Panel)
    bpy.utils.unregister_class(BLENDPRO_OT_ClearChat)
    bpy.utils.unregister_class(BLENDPRO_OT_ExportChat)
    bpy.utils.unregister_class(BLENDPRO_OT_ImportChat)
    bpy.utils.unregister_class(BLENDPRO_OT_CodePreview)
    bpy.utils.unregister_class(BLENDPRO_OT_UndoOperation)
    bpy.utils.unregister_class(BLENDPRO_OT_ShowBackups)
    bpy.utils.unregister_class(BLENDPRO_OT_CleanupBackups)
    bpy.utils.unregister_class(BLENDPRO_OT_ShowCode)
    bpy.utils.unregister_class(BLENDPRO_OT_DeleteMessage)
    bpy.utils.unregister_class(BLENDPRO_OT_TestConnection)

    # Unregister vision system operators if they were registered
    if VISION_SYSTEM_AVAILABLE:
        try:
            bpy.utils.unregister_class(BLENDPRO_OT_AnalyzeSceneVision)
            bpy.utils.unregister_class(BLENDPRO_OT_CaptureScreenshot)
            bpy.utils.unregister_class(BLENDPRO_OT_TestVisionSystem)
        except:
            pass  # Classes might not be registered if vision system failed to load

    bpy.types.VIEW3D_MT_mesh_add.remove(menu_func)
    clear_props()


if __name__ == "__main__":
    register()
