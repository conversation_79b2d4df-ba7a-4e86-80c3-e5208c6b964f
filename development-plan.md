# BlendPro AI Vision System - Detailed Development Plan

**Author:** [inkbytefo](https://github.com/inkbytefo)  
**Project:** BlendPro Advanced AI Blender Assistant  
**Target:** Revolutionary AI Vision System Implementation

## 📋 Executive Summary

This development plan outlines the implementation of a revolutionary AI vision system for BlendPro that will transform the AI assistant from a simple code generator into a true 3D artist partner. The system will enable AI to "see" and "understand" Blender scenes through multi-modal analysis combining visual screenshots with technical 3D data.

## 🎯 Project Objectives

### Primary Goals
1. **Multi-Modal Scene Intelligence**: Combine visual and technical data for comprehensive scene understanding
2. **Real-Time Scene Monitoring**: Continuous scene analysis with proactive suggestions
3. **Interactive Visual Communication**: AI-driven visual annotations and feedback
4. **Professional Scene Analysis**: Industry-standard compliance and style recognition

### Success Metrics
- Scene analysis accuracy: >90%
- Response time: <5 seconds
- User satisfaction improvement: >40%
- Professional workflow integration: >80%

## 🏗️ Current Architecture Analysis

### Existing Codebase Structure
```
BlendPro/
├── __init__.py           # Main addon registration and UI panels
├── utilities.py          # Core utilities and API communication
├── requirements.txt      # Dependencies (OpenAI client)
├── README.md            # Project documentation
└── plan.md              # Vision system requirements
```

### Current Capabilities
- ✅ AI-powered code generation via OpenAI API
- ✅ Chat history management with auto-save
- ✅ Code preview and execution safety
- ✅ Scene backup and undo functionality
- ✅ Multi-model support (GPT-4, GPT-4o, etc.)
- ✅ Advanced AI parameter configuration

### Identified Gaps for Vision System
- ❌ No screenshot capture capability
- ❌ No scene data extraction
- ❌ No multi-modal API integration
- ❌ No visual annotation system
- ❌ No real-time monitoring
- ❌ No professional analysis tools

## 🚀 Implementation Roadmap

### Phase 1: Foundation Layer (Week 1-2)
**Goal**: Establish core multi-modal capabilities

#### 1.1 Dependencies and Infrastructure
```python
# New requirements.txt additions
opencv-python>=4.8.0      # Image processing
numpy>=1.24.0             # Numerical operations  
pillow>=10.0.0            # Image handling
hashlib                   # Change detection
threading                 # Background processing
base64                    # Image encoding
```

#### 1.2 Core Utility Functions
**File**: `vision_utilities.py` (New)
- `capture_viewport_screenshot()` - Blender viewport capture
- `extract_scene_data()` - Comprehensive 3D data extraction
- `encode_image_to_base64()` - Image encoding for API
- `create_multi_view_screenshots()` - Multiple angle captures

#### 1.3 Scene Data Extraction System
**Integration Point**: Extend `utilities.py`
- Object hierarchy analysis
- Material and texture information
- Lighting setup details
- Camera and viewport data
- Modifier and constraint information

#### 1.4 GPT-4V Integration
**Integration Point**: Modify `generate_blender_code()` function
- Multi-modal message formatting
- Image attachment handling
- Enhanced prompt engineering for visual context
- Error handling for vision API calls

### Phase 2: Real-Time Intelligence (Week 3-4)
**Goal**: Implement continuous scene monitoring

#### 2.1 Scene Change Detection
**File**: `scene_monitor.py` (New)
- Hash-based change detection
- Event-driven monitoring system
- Configurable sensitivity settings
- Background processing optimization

#### 2.2 Proactive Analysis Engine
**Integration Point**: New operator classes in `__init__.py`
- Scene health assessment
- Professional standards checking
- Workflow optimization suggestions
- Performance impact analysis

#### 2.3 UI Enhancements
**Integration Point**: Extend `BLENDPRO_PT_Panel` class
- Real-time monitoring toggle
- Scene analysis status indicator
- Proactive suggestions panel
- Performance metrics display

### Phase 3: Visual Communication (Week 5-6)
**Goal**: Enable AI visual feedback and annotations

#### 3.1 Visual Annotation System
**File**: `visual_annotations.py` (New)
- 3D overlay rendering system
- Object highlighting capabilities
- Spatial reference arrows
- Interactive annotation management

#### 3.2 AI Response Parser
**Integration Point**: Enhance response processing
- Visual command parsing
- Annotation instruction extraction
- Spatial coordinate handling
- Interactive element creation

#### 3.3 Enhanced UI Components
**Integration Point**: New panel sections
- Annotation control panel
- Visual feedback settings
- Interactive object picker
- Annotation history viewer

### Phase 4: Professional Analysis (Week 7-8)
**Goal**: Industry-standard analysis and style recognition

#### 4.1 Scene DNA Extraction
**File**: `scene_analyzer.py` (New)
- Composition analysis algorithms
- Color palette extraction
- Lighting pattern recognition
- Technical quality assessment

#### 4.2 Professional Standards Database
**File**: `professional_standards.py` (New)
- Industry-specific guidelines
- Compliance checking algorithms
- Quality metrics calculation
- Best practice recommendations

#### 4.3 Style Transfer Integration
**Integration Point**: Advanced analysis features
- Style pattern recognition
- Modification suggestions
- Workflow optimization
- Professional feedback system

## 🔧 Technical Implementation Details

### New Operator Classes Required
```python
# Add to __init__.py registration
BLENDPRO_OT_AnalyzeScene         # Multi-modal scene analysis
BLENDPRO_OT_ToggleMonitoring     # Real-time monitoring control
BLENDPRO_OT_ClearAnnotations     # Visual annotation management
BLENDPRO_OT_ExtractSceneDNA      # Professional analysis
BLENDPRO_OT_CaptureScreenshot    # Manual screenshot capture
BLENDPRO_OT_ShowSceneHealth      # Health assessment display
```

### Database Integration
- **Scene Templates**: Common scene patterns and configurations
- **Style Guides**: Industry-specific style references
- **Best Practices**: Professional workflow standards
- **Quality Metrics**: Technical assessment criteria

### Performance Optimization Strategy
- **Caching System**: Scene data and screenshot caching
- **Background Processing**: Threaded analysis operations
- **Progressive Loading**: Incremental data processing
- **Memory Management**: Efficient resource utilization

## 🧪 Testing Strategy

### Unit Testing Framework
```python
# New test files structure
tests/
├── test_vision_utilities.py     # Screenshot and data extraction
├── test_scene_monitor.py        # Change detection algorithms
├── test_visual_annotations.py   # Annotation system
├── test_scene_analyzer.py       # Professional analysis
└── test_integration.py          # End-to-end workflows
```

### Integration Testing Scenarios
1. **Multi-Modal API Integration**: Vision + text processing
2. **Real-Time Performance**: Monitoring system efficiency
3. **Visual Annotation Accuracy**: Overlay precision testing
4. **Professional Analysis Validation**: Industry standard compliance

### User Acceptance Testing
- **Workflow Integration**: Professional artist feedback
- **Performance Benchmarks**: Response time measurements
- **Feature Adoption**: Usage pattern analysis
- **Quality Assessment**: Output accuracy evaluation

## 📊 Risk Assessment and Mitigation

### Technical Risks
| Risk | Impact | Probability | Mitigation Strategy |
|------|--------|-------------|-------------------|
| GPT-4V API Limitations | High | Medium | Fallback to text-only mode |
| Performance Degradation | Medium | High | Optimization and caching |
| Blender API Changes | Medium | Low | Version compatibility testing |
| Memory Usage Issues | Medium | Medium | Resource monitoring and cleanup |

### Development Risks
| Risk | Impact | Probability | Mitigation Strategy |
|------|--------|-------------|-------------------|
| Timeline Delays | Medium | Medium | Phased delivery approach |
| Feature Complexity | High | Medium | MVP-first development |
| Integration Challenges | Medium | High | Incremental integration |
| Testing Coverage | Medium | Low | Automated testing framework |

## 📈 Success Measurement Framework

### Technical KPIs
- **Response Time**: <5 seconds for scene analysis
- **Accuracy Rate**: >90% for scene understanding
- **Memory Usage**: <500MB additional overhead
- **CPU Impact**: <30% during analysis operations

### User Experience KPIs
- **Feature Adoption**: >70% of users utilizing vision features
- **Workflow Efficiency**: >40% improvement in task completion
- **User Satisfaction**: >4.5/5 rating for new features
- **Professional Approval**: >80% positive feedback from industry users

### Business Impact KPIs
- **User Retention**: Improved long-term engagement
- **Feature Differentiation**: Unique market positioning
- **Community Growth**: Increased adoption and contributions
- **Industry Recognition**: Professional endorsements and reviews

## 🔄 Deployment Strategy

### Rollout Phases
1. **Alpha Release**: Core team testing (Week 9)
2. **Beta Release**: Limited user group (Week 10)
3. **Release Candidate**: Public testing (Week 11)
4. **Production Release**: Full deployment (Week 12)

### Backward Compatibility
- Maintain existing API compatibility
- Graceful degradation for unsupported features
- Migration tools for existing configurations
- Documentation for upgrade procedures

## 📚 Documentation Requirements

### Developer Documentation
- **API Reference**: New functions and classes
- **Architecture Guide**: System design and integration
- **Performance Guide**: Optimization best practices
- **Testing Guide**: Framework and procedures

### User Documentation
- **Feature Guide**: Vision system capabilities
- **Workflow Examples**: Professional use cases
- **Troubleshooting**: Common issues and solutions
- **Best Practices**: Optimal usage patterns

## 🎯 Next Steps

### Immediate Actions (Week 1)
1. Set up development environment with new dependencies
2. Create foundation files: `vision_utilities.py`, `scene_monitor.py`
3. Implement basic screenshot capture functionality
4. Begin scene data extraction system development

### Short-term Goals (Week 2-4)
1. Complete Phase 1 implementation
2. Begin Phase 2 development
3. Establish testing framework
4. Create initial documentation

### Long-term Objectives (Week 5-12)
1. Complete all four phases
2. Comprehensive testing and optimization
3. User feedback integration
4. Production deployment preparation

## 💻 Detailed Code Implementation Guide

### Core Vision System Architecture

#### 1. Scene Data Extraction Implementation
```python
# vision_utilities.py - Core scene analysis functions

import bpy
import bmesh
import mathutils
import json
from typing import Dict, List, Any, Optional

def extract_comprehensive_scene_data(context) -> Dict[str, Any]:
    """Extract detailed scene information for AI analysis"""
    scene_data = {
        "metadata": {
            "blender_version": bpy.app.version_string,
            "scene_name": context.scene.name,
            "frame_current": context.scene.frame_current,
            "frame_range": [context.scene.frame_start, context.scene.frame_end],
            "render_engine": context.scene.render.engine,
            "timestamp": time.time()
        },
        "objects": extract_object_data(context),
        "materials": extract_material_data(context),
        "lights": extract_lighting_data(context),
        "cameras": extract_camera_data(context),
        "world": extract_world_data(context),
        "scene_properties": extract_scene_properties(context),
        "viewport_info": extract_viewport_info(context),
        "hierarchy": extract_object_hierarchy(context)
    }
    return scene_data

def extract_object_data(context) -> List[Dict[str, Any]]:
    """Extract detailed object information"""
    objects_data = []

    for obj in context.scene.objects:
        obj_data = {
            "name": obj.name,
            "type": obj.type,
            "location": list(obj.location),
            "rotation_euler": list(obj.rotation_euler),
            "rotation_quaternion": list(obj.rotation_quaternion),
            "scale": list(obj.scale),
            "dimensions": list(obj.dimensions),
            "visible": obj.visible_get(),
            "hide_viewport": obj.hide_viewport,
            "hide_render": obj.hide_render,
            "material_slots": [],
            "modifiers": [],
            "constraints": [],
            "parent": obj.parent.name if obj.parent else None,
            "children": [child.name for child in obj.children],
            "collections": [col.name for col in obj.users_collection]
        }

        # Material slots information
        for slot in obj.material_slots:
            if slot.material:
                obj_data["material_slots"].append({
                    "name": slot.material.name,
                    "slot_index": slot.slot_index,
                    "link": slot.link
                })

        # Modifiers information
        for mod in obj.modifiers:
            mod_data = {
                "name": mod.name,
                "type": mod.type,
                "show_viewport": mod.show_viewport,
                "show_render": mod.show_render
            }
            # Add specific modifier properties based on type
            if mod.type == 'SUBSURF':
                mod_data["levels"] = mod.levels
                mod_data["render_levels"] = mod.render_levels
            elif mod.type == 'MIRROR':
                mod_data["use_axis"] = [mod.use_axis[0], mod.use_axis[1], mod.use_axis[2]]
                mod_data["mirror_object"] = mod.mirror_object.name if mod.mirror_object else None

            obj_data["modifiers"].append(mod_data)

        # Constraints information
        for constraint in obj.constraints:
            obj_data["constraints"].append({
                "name": constraint.name,
                "type": constraint.type,
                "influence": constraint.influence,
                "mute": constraint.mute
            })

        # Mesh-specific data
        if obj.type == 'MESH' and obj.data:
            mesh_data = extract_mesh_data(obj)
            obj_data["mesh_data"] = mesh_data

        objects_data.append(obj_data)

    return objects_data

def extract_mesh_data(obj) -> Dict[str, Any]:
    """Extract detailed mesh information"""
    mesh = obj.data

    # Create bmesh for analysis
    bm = bmesh.new()
    bm.from_mesh(mesh)
    bm.faces.ensure_lookup_table()
    bm.verts.ensure_lookup_table()
    bm.edges.ensure_lookup_table()

    mesh_data = {
        "vertex_count": len(bm.verts),
        "edge_count": len(bm.edges),
        "face_count": len(bm.faces),
        "polygon_count": len(mesh.polygons),
        "has_custom_normals": mesh.has_custom_normals,
        "uv_layers": [uv.name for uv in mesh.uv_layers],
        "vertex_colors": [vc.name for vc in mesh.vertex_colors],
        "shape_keys": []
    }

    # Shape keys information
    if obj.data.shape_keys:
        for key in obj.data.shape_keys.key_blocks:
            mesh_data["shape_keys"].append({
                "name": key.name,
                "value": key.value,
                "slider_min": key.slider_min,
                "slider_max": key.slider_max
            })

    # Topology analysis
    mesh_data["topology_analysis"] = analyze_mesh_topology(bm)

    bm.free()
    return mesh_data

def analyze_mesh_topology(bm) -> Dict[str, Any]:
    """Analyze mesh topology for quality assessment"""
    analysis = {
        "triangles": 0,
        "quads": 0,
        "ngons": 0,
        "non_manifold_edges": 0,
        "loose_vertices": 0,
        "loose_edges": 0
    }

    # Face analysis
    for face in bm.faces:
        if len(face.verts) == 3:
            analysis["triangles"] += 1
        elif len(face.verts) == 4:
            analysis["quads"] += 1
        else:
            analysis["ngons"] += 1

    # Edge analysis
    for edge in bm.edges:
        if not edge.is_manifold:
            analysis["non_manifold_edges"] += 1

    # Vertex analysis
    for vert in bm.verts:
        if len(vert.link_edges) == 0:
            analysis["loose_vertices"] += 1

    # Edge analysis for loose edges
    for edge in bm.edges:
        if len(edge.link_faces) == 0:
            analysis["loose_edges"] += 1

    return analysis
```

#### 2. Screenshot Capture System
```python
# vision_utilities.py - Screenshot capture functions

import bpy
import gpu
import bgl
from gpu_extras.presets import draw_texture_2d
import numpy as np
from PIL import Image
import base64
import io

def capture_viewport_screenshot(context, resolution=(1920, 1080)) -> str:
    """Capture current viewport as base64 encoded image"""
    try:
        # Get current 3D viewport
        area = None
        for area in context.screen.areas:
            if area.type == 'VIEW_3D':
                break

        if not area:
            raise Exception("No 3D viewport found")

        # Get the region
        region = None
        for region in area.regions:
            if region.type == 'WINDOW':
                break

        if not region:
            raise Exception("No viewport region found")

        # Capture the viewport
        with context.temp_override(area=area, region=region):
            # Set viewport shading for better visibility
            space = area.spaces.active
            original_shading = space.shading.type

            # Temporarily set to material preview for better AI analysis
            space.shading.type = 'MATERIAL_PREVIEW'

            # Force viewport update
            bpy.ops.wm.redraw_timer(type='DRAW_WIN_SWAP', iterations=1)

            # Capture the image
            buffer = bgl.Buffer(bgl.GL_BYTE, region.width * region.height * 4)
            bgl.glReadPixels(0, 0, region.width, region.height, bgl.GL_RGBA, bgl.GL_UNSIGNED_BYTE, buffer)

            # Convert to numpy array and flip vertically
            image_array = np.frombuffer(buffer, dtype=np.uint8)
            image_array = image_array.reshape(region.height, region.width, 4)
            image_array = np.flipud(image_array)

            # Convert to PIL Image
            image = Image.fromarray(image_array, 'RGBA')

            # Resize if needed
            if image.size != resolution:
                image = image.resize(resolution, Image.Resampling.LANCZOS)

            # Convert to RGB (remove alpha channel)
            image = image.convert('RGB')

            # Restore original shading
            space.shading.type = original_shading

            # Encode to base64
            buffer = io.BytesIO()
            image.save(buffer, format='PNG')
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

            return image_base64

    except Exception as e:
        print(f"Error capturing screenshot: {e}")
        return None

def capture_multi_view_screenshots(context) -> Dict[str, str]:
    """Capture screenshots from multiple predefined viewpoints"""
    screenshots = {}

    # Store original view settings
    area = None
    for area in context.screen.areas:
        if area.type == 'VIEW_3D':
            break

    if not area:
        return screenshots

    space = area.spaces.active
    region_3d = space.region_3d

    # Store original view matrix
    original_view_matrix = region_3d.view_matrix.copy()
    original_view_distance = region_3d.view_distance
    original_view_location = region_3d.view_location.copy()

    # Define viewpoints
    viewpoints = {
        "perspective": {
            "view_rotation": mathutils.Quaternion((0.7071, 0.7071, 0.0, 0.0)),
            "description": "Default perspective view"
        },
        "front": {
            "view_rotation": mathutils.Quaternion((1.0, 0.0, 0.0, 0.0)),
            "description": "Front orthographic view"
        },
        "right": {
            "view_rotation": mathutils.Quaternion((0.7071, 0.0, 0.0, 0.7071)),
            "description": "Right side orthographic view"
        },
        "top": {
            "view_rotation": mathutils.Quaternion((0.0, 0.0, 0.0, 1.0)),
            "description": "Top orthographic view"
        }
    }

    try:
        for view_name, view_data in viewpoints.items():
            # Set the view
            region_3d.view_rotation = view_data["view_rotation"]

            # Frame all objects for better view
            bpy.ops.view3d.view_all()

            # Force viewport update
            bpy.ops.wm.redraw_timer(type='DRAW_WIN_SWAP', iterations=1)

            # Capture screenshot
            screenshot = capture_viewport_screenshot(context)
            if screenshot:
                screenshots[view_name] = screenshot

    except Exception as e:
        print(f"Error capturing multi-view screenshots: {e}")

    finally:
        # Restore original view
        try:
            region_3d.view_matrix = original_view_matrix
            region_3d.view_distance = original_view_distance
            region_3d.view_location = original_view_location
        except Exception as e:
            print(f"Error restoring view: {e}")

    return screenshots
```

#### 3. Multi-Modal API Integration
```python
# vision_utilities.py - GPT-4V integration

def analyze_scene_with_vision(context, user_prompt: str, api_key: str, base_url: str = None, model: str = "gpt-4-vision-preview") -> Dict[str, Any]:
    """Perform multi-modal scene analysis using GPT-4V"""

    try:
        # Extract scene data
        scene_data = extract_comprehensive_scene_data(context)

        # Capture screenshots
        screenshots = capture_multi_view_screenshots(context)

        if not screenshots:
            # Fallback to single screenshot
            single_screenshot = capture_viewport_screenshot(context)
            if single_screenshot:
                screenshots = {"main": single_screenshot}

        if not screenshots:
            return {"error": "Failed to capture any screenshots", "code": None}

        # Create OpenAI client
        client_kwargs = {"api_key": api_key, "timeout": 60}
        if base_url and base_url.strip():
            client_kwargs["base_url"] = base_url

        client = OpenAI(**client_kwargs)

        # Prepare multi-modal messages
        messages = [
            {
                "role": "system",
                "content": """You are a professional 3D artist assistant with advanced understanding of Blender.
                You can see the 3D scene from multiple angles and have access to detailed technical scene data.

                Your capabilities include:
                - Analyzing 3D composition and spatial relationships
                - Understanding lighting setups and material properties
                - Recognizing modeling techniques and topology
                - Providing contextual, spatial-aware responses
                - Generating precise Blender Python code based on visual analysis

                When responding:
                1. Reference specific objects you can see in the images
                2. Consider the technical data provided
                3. Provide actionable Blender Python code
                4. Explain your visual observations when relevant

                Available visual annotation commands (use when helpful):
                - [HIGHLIGHT:object_name] - Highlight specific object
                - [POINT_TO:x,y,z] - Point to specific coordinates
                - [CIRCLE:object_name] - Draw circle around object
                - [ARROW:from_obj,to_obj] - Draw arrow between objects"""
            }
        ]

        # Add screenshots to messages
        for view_name, screenshot_data in screenshots.items():
            messages.append({
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": f"Scene view from {view_name} angle:"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{screenshot_data}",
                            "detail": "high"
                        }
                    }
                ]
            })

        # Add technical scene data
        scene_summary = create_scene_summary(scene_data)
        messages.append({
            "role": "user",
            "content": f"""Technical Scene Data Summary:

{scene_summary}

User Request: {user_prompt}

Please analyze the scene visually and technically, then provide a response that takes into account both the visual elements you can see and the technical data provided. If generating code, make it specific to the objects and setup visible in the scene."""
        })

        # Make API call
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            max_tokens=2000,
            temperature=0.7
        )

        if response.choices and response.choices[0].message:
            content = response.choices[0].message.content

            # Parse response for code and annotations
            parsed_response = parse_vision_response(content, scene_data)

            return {
                "error": None,
                "code": parsed_response.get("code"),
                "analysis": parsed_response.get("analysis"),
                "annotations": parsed_response.get("annotations"),
                "full_response": content
            }
        else:
            return {"error": "No response from vision API", "code": None}

    except Exception as e:
        return {"error": f"Vision analysis failed: {str(e)}", "code": None}

def create_scene_summary(scene_data: Dict[str, Any]) -> str:
    """Create a concise summary of scene data for AI analysis"""

    objects = scene_data.get("objects", [])
    materials = scene_data.get("materials", [])
    lights = scene_data.get("lights", [])
    cameras = scene_data.get("cameras", [])

    summary = f"""Scene Overview:
- Objects: {len(objects)} total
- Materials: {len(materials)} total
- Lights: {len(lights)} total
- Cameras: {len(cameras)} total
- Render Engine: {scene_data.get('metadata', {}).get('render_engine', 'Unknown')}

Object Details:"""

    # Add object information
    for obj in objects[:10]:  # Limit to first 10 objects
        obj_type = obj.get("type", "Unknown")
        obj_name = obj.get("name", "Unnamed")
        location = obj.get("location", [0, 0, 0])

        summary += f"\n- {obj_name} ({obj_type}) at {location}"

        if obj.get("material_slots"):
            materials_list = [slot.get("name", "Unknown") for slot in obj["material_slots"]]
            summary += f" | Materials: {', '.join(materials_list)}"

        if obj.get("modifiers"):
            modifiers_list = [mod.get("name", "Unknown") for mod in obj["modifiers"]]
            summary += f" | Modifiers: {', '.join(modifiers_list)}"

    if len(objects) > 10:
        summary += f"\n... and {len(objects) - 10} more objects"

    # Add lighting information
    if lights:
        summary += f"\n\nLighting Setup:"
        for light in lights:
            light_name = light.get("name", "Unknown")
            light_type = light.get("light_type", "Unknown")
            energy = light.get("energy", "Unknown")
            summary += f"\n- {light_name} ({light_type}) | Energy: {energy}"

    return summary

def parse_vision_response(response_content: str, scene_data: Dict[str, Any]) -> Dict[str, Any]:
    """Parse AI response for code, analysis, and visual annotations"""

    parsed = {
        "code": None,
        "analysis": "",
        "annotations": []
    }

    # Extract code blocks
    code_matches = re.findall(r'```(?:python)?\s*(.*?)```', response_content, re.DOTALL)
    if code_matches:
        parsed["code"] = code_matches[0].strip()

    # Extract visual annotations
    annotations = []

    # Highlight commands
    highlight_matches = re.findall(r'\[HIGHLIGHT:(\w+)\]', response_content)
    for obj_name in highlight_matches:
        annotations.append({
            "type": "highlight",
            "target": obj_name,
            "color": (1, 1, 0, 0.7)  # Yellow highlight
        })

    # Point-to commands
    point_matches = re.findall(r'\[POINT_TO:([\d\.\-,\s]+)\]', response_content)
    for coords_str in point_matches:
        try:
            coords = [float(x.strip()) for x in coords_str.split(',')]
            if len(coords) == 3:
                annotations.append({
                    "type": "point",
                    "location": coords,
                    "color": (0, 1, 0, 1)  # Green point
                })
        except ValueError:
            continue

    # Circle commands
    circle_matches = re.findall(r'\[CIRCLE:(\w+)\]', response_content)
    for obj_name in circle_matches:
        annotations.append({
            "type": "circle",
            "target": obj_name,
            "color": (0, 0, 1, 0.8)  # Blue circle
        })

    # Arrow commands
    arrow_matches = re.findall(r'\[ARROW:(\w+),(\w+)\]', response_content)
    for from_obj, to_obj in arrow_matches:
        annotations.append({
            "type": "arrow",
            "from_object": from_obj,
            "to_object": to_obj,
            "color": (1, 0, 1, 0.9)  # Magenta arrow
        })

    parsed["annotations"] = annotations

    # Extract analysis text (everything that's not code or annotations)
    analysis_text = response_content

    # Remove code blocks
    analysis_text = re.sub(r'```.*?```', '', analysis_text, flags=re.DOTALL)

    # Remove annotation commands
    analysis_text = re.sub(r'\[(?:HIGHLIGHT|POINT_TO|CIRCLE|ARROW):[^\]]+\]', '', analysis_text)

    parsed["analysis"] = analysis_text.strip()

    return parsed
```

---

**This development plan provides a comprehensive roadmap for transforming BlendPro into a revolutionary AI vision-powered 3D assistant, establishing it as the industry leader in AI-assisted 3D creation workflows.**
